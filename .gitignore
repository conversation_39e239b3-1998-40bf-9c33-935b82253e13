# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
main
peerchat-cli
peerchat-api

# Build directories
bin/
dist/
pkg/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Flutter/Dart specific
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/
**/android/app/debug
**/android/app/profile
**/android/app/release

# Environment files
*.env
*.local
*-lock.json

# Log files
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
dist/
release/

# Development files
TODO.md
web/