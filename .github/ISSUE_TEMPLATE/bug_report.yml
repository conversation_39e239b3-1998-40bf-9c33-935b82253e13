name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[BUG] "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for reporting a bug! Please fill out the information below to help us understand and reproduce the issue.

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Run command '...'
        2. Click on '...'
        3. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should have happened?
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: A clear and concise description of what actually happened.
      placeholder: What actually happened?
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Environment Information
      description: Please provide information about your environment
      placeholder: |
        - OS: [e.g. Ubuntu 22.04, macOS 13.0, Windows 11]
        - Go version: [e.g. 1.21.0]
        - Xelvra version: [e.g. v0.1.0-alpha]
        - Network: [e.g. WiFi, Ethernet, Mobile]
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Relevant Logs
      description: Please copy and paste any relevant log output
      placeholder: |
        Paste logs here (from ~/.xelvra/peerchat.log or console output)
      render: shell

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here
      placeholder: Any additional information that might be helpful...

  - type: checkboxes
    id: checklist
    attributes:
      label: Pre-submission Checklist
      description: Please check the following before submitting
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have run `peerchat-cli doctor` to check for common issues
          required: false
        - label: I have included relevant logs and error messages
          required: false
        - label: I have provided steps to reproduce the issue
          required: true
