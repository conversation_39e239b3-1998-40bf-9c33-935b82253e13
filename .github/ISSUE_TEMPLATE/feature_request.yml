name: ✨ Feature Request
description: Suggest a new feature or enhancement
title: "[FEATURE] "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for suggesting a new feature! Please provide as much detail as possible to help us understand your request.

  - type: textarea
    id: summary
    attributes:
      label: Feature Summary
      description: A brief summary of the feature you'd like to see
      placeholder: Briefly describe the feature...
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: What problem does this feature solve? What use case does it address?
      placeholder: |
        Is your feature request related to a problem? Please describe.
        A clear and concise description of what the problem is.
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see
      placeholder: |
        A clear and concise description of what you want to happen.
        How should this feature work?
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternative Solutions
      description: Describe any alternative solutions or features you've considered
      placeholder: |
        A clear and concise description of any alternative solutions or features you've considered.

  - type: dropdown
    id: priority
    attributes:
      label: Priority Level
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would be helpful
        - High - Important for my use case
        - Critical - Blocking my usage
    validations:
      required: true

  - type: dropdown
    id: component
    attributes:
      label: Component
      description: Which part of Xelvra does this feature relate to?
      options:
        - CLI - Command line interface
        - P2P Core - Networking and peer communication
        - Security - Encryption and authentication
        - API - gRPC API service
        - GUI - Graphical user interface
        - Documentation - Docs and guides
        - Other - Please specify in additional context
    validations:
      required: true

  - type: textarea
    id: implementation
    attributes:
      label: Implementation Ideas
      description: Do you have any ideas about how this could be implemented?
      placeholder: |
        If you have technical ideas about implementation, please share them here.
        This is optional but can be very helpful.

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context, screenshots, or examples about the feature request
      placeholder: |
        Any additional information that might be helpful...
        Screenshots, mockups, examples from other applications, etc.

  - type: checkboxes
    id: checklist
    attributes:
      label: Pre-submission Checklist
      description: Please check the following before submitting
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have considered if this fits with Xelvra's goals and philosophy
          required: true
        - label: I have provided a clear use case for this feature
          required: true
